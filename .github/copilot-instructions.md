# Copilot Instructions for SaaS Template v2

## Architecture Overview

This is a production-ready SaaS template built with Next.js 15 App Router, featuring a modular architecture:

- **Database Layer**: PostgreSQL with Prisma ORM for type-safe database operations
- **Authentication**: Better Auth with multi-provider support (Google, GitHub, email/password, passkeys)
- **API Layer**: tRPC for type-safe API routes with React Query client-side caching
- **Payment Processing**: Stripe integration with subscription management via Better Auth Stripe plugin
- **File Storage**: AWS S3-compatible storage (Tigris Data) via Better Upload
- **Email System**: React Email templates with Resend delivery
- **UI Components**: shadcn/ui with Tailwind CSS and Radix UI primitives

## Critical Patterns & Conventions

### Authentication & Authorization

- Use `getAuthSession()` from `@/lib/auth` for server-side session checking
- Protected routes use `protectedProcedure` in tRPC routers
- Admin features check for `admin` or `superadmin` roles via Better Auth admin plugin
- All auth logic centralized in `src/lib/auth.ts` with Better Auth configuration

### Database & Queries

- Prisma schema defines User, Session, Account, Verification, Passkey, and Subscription models
- Use tRPC procedures for all database operations - no direct Prisma calls in components
- Database context injected via `createTRPCContext` in `src/server/api/trpc.ts`
- Run `bun run db:generate` after schema changes to update Prisma client

### tRPC API Structure

- Routers organized by domain: `user`, `storage`, `payments` in `src/server/api/routers/`
- Use `protectedProcedure` for authenticated endpoints, `publicProcedure` for public ones
- Input validation with Zod schemas defined in `src/schemas/`
- Error handling with ZodError formatting built into tRPC config

### Component Organization

- UI components in `src/components/ui/` follow shadcn/ui patterns
- Feature components grouped by domain: `auth/`, `dashboard/`, `settings/`, `admin/`
- Use `cn()` utility from `src/lib/utils.ts` for className merging (clsx + tailwind-merge)
- Button component supports loading states, icons, and href prop for Link integration

### File Uploads & Storage

- Use Better Upload for file handling with S3-compatible storage
- Upload components in `src/components/ui/image-uploader.tsx` and `upload-dropzone-progress.tsx`
- Storage operations via tRPC `storage` router
- S3 config in `src/lib/s3.ts` with presigned URL generation

### Email Templates

- React Email templates in `src/emails/templates/` with shared components in `src/emails/components/`
- Email sending logic in `src/lib/mail.ts` using Resend
- Preview emails locally with `bun run preview-emails`

### Environment & Configuration

- Type-safe environment variables via `@t3-oss/env-nextjs` in `src/env.js`
- Constants in `src/lib/contants.ts` including app name and public routes
- Stripe webhook handling requires `STRIPE_WEBHOOK_SECRET` for Better Auth integration

## Development Workflows

### Database Operations

```bash
bun run db:generate    # Run migrations and generate Prisma client
bun run db:studio     # Open Prisma Studio for data viewing
bun run db:push       # Push schema changes without migrations
```

### Development Server

```bash
bun dev               # Start with Turbo for fast refresh
./start-database.sh   # Start local PostgreSQL container
```

### Stripe Integration

```bash
bun run stripe:listen # Forward webhooks to local development
```

### Code Quality

```bash
bun run check        # Run linting and type checking
bun run format:write # Format code with Prettier
```

## Key Integration Points

### Better Auth Configuration

- Multi-provider setup in `src/lib/auth.ts` with Stripe plugin for subscription handling
- Passkey support configured for localhost (update `rpID` and `origin` for production)
- Email verification and password reset flows integrated with React Email templates

### Stripe Subscription Flow

- Subscription model linked via `stripeCustomerId` and `stripeSubscriptionId`
- Pro plan with 14-day trial configured in Better Auth Stripe plugin
- Webhook endpoints handle subscription status changes automatically

### File Upload Security

- S3 bucket configured with presigned URLs for secure uploads
- File cleanup handled via storage router when entities are deleted
- Avatar generation for users without profile pictures

## Common Gotchas

- Always use absolute imports with `@/` prefix as configured in `tsconfig.json`
- Prisma client regeneration required after schema changes via `bun run db:generate`
- tRPC procedures must be added to appropriate router in `src/server/api/root.ts`
- Environment variables must be added to both schema and runtime mapping in `src/env.js`
- Better Auth requires `better-auth:generate` command after auth config changes
- Stripe webhook secret must match between Stripe CLI and environment variables
