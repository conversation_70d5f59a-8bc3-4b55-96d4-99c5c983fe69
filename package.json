{"name": "saas-template-v2", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "better-auth:generate": "npx @better-auth/cli generate --config ./src/lib/auth.ts", "db:generate": "prisma migrate dev", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "dev": "next dev --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "postinstall": "prisma generate", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "preview-emails": "email dev --dir ./src/emails/templates --port 3333", "stripe:listen": "stripe listen --forward-to localhost:3000/api/auth/stripe/webhook"}, "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "@aws-sdk/s3-request-presigner": "^3.844.0", "@better-auth/stripe": "^1.2.12", "@better-fetch/fetch": "^1.1.18", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@react-email/components": "^0.0.36", "@react-email/tailwind": "^1.2.1", "@t3-oss/env-nextjs": "^0.12.0", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.83.0", "@trpc/client": "^11.4.3", "@trpc/react-query": "^11.4.3", "@trpc/server": "^11.4.3", "better-auth": "^1.2.12", "better-upload": "^0.3.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dayjs": "^1.11.13", "geist": "^1.4.2", "lucide-react": "^0.487.0", "nanoid": "^5.1.5", "next": "^15.3.5", "nextjs-toploader": "^3.8.16", "radash": "^12.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-email": "^4.1.3", "react-hook-form": "^7.60.0", "resend": "^4.6.0", "server-only": "^0.0.1", "sonner": "^2.0.6", "stripe": "^18.3.0", "superjson": "^2.2.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.5", "ua-parser-js": "^2.0.4", "vaul": "^1.1.2", "zod": "^3.25.76", "zod-error": "^1.5.0", "zod-openapi": "^4.2.4"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@react-email/preview-server": "4.1.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/eslint": "^9.6.1", "@types/node": "^22.16.3", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "eslint-config-next": "^15.3.5", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "prisma": "^6.11.1", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "ct3aMetadata": {"initVersion": "7.38.1"}}